# Automatic Pod Restarts on Helm Deployment

## Problem

Previously, when deploying with Helm, Kubernetes pods would not automatically restart even when new Docker images were built and pushed. This happened because:

1. Images use the `latest` tag
2. Kubernetes doesn't detect image changes when the tag stays the same
3. `imagePullPolicy: Always` only pulls new images when pods are restarted, but doesn't trigger restarts

## Solution

Added automatic pod restart functionality by including a timestamp annotation in all deployment templates. This forces Kubernetes to restart pods on every Helm deployment.

## Changes Made

### 1. Updated Deployment Templates

Added the following annotation to all deployment templates:

```yaml
annotations:
  # Force pod restart on each deployment
  deployment.kubernetes.io/restart: {{ now | quote }}
```

**Files modified:**
- `helm/warda/charts/backend/templates/deployment.yaml`
- `helm/warda/charts/frontend/templates/deployment.yaml`
- `helm/warda/charts/keycloak/templates/deployment.yaml`
- `helm/warda/charts/postgresql/templates/deployment.yaml`
- `helm/warda/charts/migrate/templates/migrate-job.yaml`

### 2. Enhanced Makefile Commands

Added new restart commands for development:

```bash
make restart-frontend   # Restart just the frontend deployment
make restart-backend    # Restart just the backend deployment  
make restart-all        # Restart all deployments
```

### 3. Test Script

Created `scripts/test-auto-restart.sh` to verify the functionality works correctly.

## How It Works

1. **Helm Template Processing**: When Helm processes templates, `{{ now | quote }}` generates the current timestamp
2. **Annotation Change**: Each deployment gets a unique timestamp annotation
3. **Kubernetes Detection**: Kubernetes detects the annotation change as a pod template modification
4. **Rolling Update**: Kubernetes performs a rolling update, restarting pods with the new configuration
5. **Image Pull**: With `imagePullPolicy: Always`, new pods pull the latest images

## Benefits

- **Automatic**: No manual intervention needed
- **Reliable**: Pods always restart when deploying
- **Safe**: Uses Kubernetes rolling update strategy (zero downtime)
- **Consistent**: Works for all deployments (frontend, backend, keycloak, postgresql, migrate)

## Usage

### Normal Deployment
```bash
make helm-deploy        # Pods will restart automatically
make k3d-deploy         # Build and deploy with automatic restarts
```

### Manual Restarts (if needed)
```bash
make restart-frontend   # Restart just frontend
make restart-backend    # Restart just backend
make restart-all        # Restart everything
```

### Testing
```bash
./scripts/test-auto-restart.sh   # Verify automatic restart functionality
```

## Verification

After deployment, you can verify pods restarted by checking:

1. **Pod creation times**:
   ```bash
   kubectl get pods -l app.kubernetes.io/instance=warda -o wide
   ```

2. **Restart annotations**:
   ```bash
   kubectl get deployment frontend -o jsonpath='{.spec.template.metadata.annotations.deployment\.kubernetes\.io/restart}'
   ```

## Rollback Strategy

If you need to disable automatic restarts:

1. Remove the annotation from deployment templates
2. Redeploy with Helm
3. Use manual restart commands when needed

## Notes

- The timestamp annotation is harmless and doesn't affect pod functionality
- Rolling update strategy ensures zero downtime
- Works with both local k3d and remote k3s clusters
- Compatible with existing CI/CD workflows
