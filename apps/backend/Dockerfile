FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace manifests first (for dependency caching)
COPY Cargo.toml Cargo.lock ./
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/backend/Config.toml ./apps/backend/
COPY apps/frontend/Cargo.toml ./apps/frontend/
COPY apps/migrate/Cargo.toml ./apps/migrate/
COPY crates/shared/Cargo.toml ./crates/shared/

# Create dummy source files to build dependencies
RUN mkdir -p apps/backend/src apps/frontend/src apps/migrate/src crates/shared/src && \
    echo "fn main() {}" > apps/backend/src/main.rs && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub fn hello() {}" > apps/frontend/src/lib.rs && \
    echo "fn main() {}" > apps/migrate/src/main.rs && \
    echo "pub fn hello() {}" > crates/shared/src/lib.rs

# Build dependencies only with cache mount (cached layer)
RUN --mount=type=cache,target=/usr/local/cargo/registry,id=backend-registry \
    --mount=type=cache,target=/app/target,id=backend-target \
    cargo build --release --package backend

# Remove dummy files and copy actual source
RUN rm -rf apps/backend/src crates/shared/src
COPY apps/backend ./apps/backend/
COPY crates/shared/src ./crates/shared/src/

# Build the actual application with cache mount
RUN --mount=type=cache,target=/usr/local/cargo/registry,id=backend-registry \
    --mount=type=cache,target=/app/target,id=backend-target \
    cargo build --release --package backend && \
    cp target/release/backend /usr/local/bin/backend

FROM debian:bookworm-slim AS runtime

RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

COPY --from=builder /usr/local/bin/backend /usr/local/bin/backend
RUN mkdir /config/
COPY apps/backend/Config.toml /config/Config.toml

ENTRYPOINT ["/usr/local/bin/backend"]