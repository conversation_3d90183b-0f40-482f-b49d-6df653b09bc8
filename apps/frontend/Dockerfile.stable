FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace manifests first (for dependency caching)
COPY Cargo.toml Cargo.lock ./
COPY apps/frontend/Cargo.toml ./apps/frontend/
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/migrate/Cargo.toml ./apps/migrate/
COPY crates/shared/Cargo.toml ./crates/shared/

# Install Rust tools and target with exact versions from lock file
RUN rustup target add wasm32-unknown-unknown && \
    WASM_BINDGEN_VERSION=$(grep -A1 'name = "wasm-bindgen"' Cargo.lock | grep version | head -1 | cut -d'"' -f2) && \
    echo "Installing wasm-bindgen-cli version: $WASM_BINDGEN_VERSION" && \
    cargo install wasm-bindgen-cli --version $WASM_BINDGEN_VERSION && \
    cargo install trunk --version 0.21.14

# Create dummy source files to build dependencies
RUN mkdir -p apps/frontend/src apps/backend/src apps/migrate/src crates/shared/src && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub fn hello() {}" > apps/frontend/src/lib.rs && \
    echo "fn main() {}" > apps/backend/src/main.rs && \
    echo "fn main() {}" > apps/migrate/src/main.rs && \
    echo "pub fn hello() {}" > crates/shared/src/lib.rs

# Build dependencies only (cached layer)
WORKDIR /app/apps/frontend
RUN cargo build --target=wasm32-unknown-unknown --release
RUN rm -rf src ../../crates/shared/src

# Copy actual source code and configuration
COPY apps/frontend/src ./src/
COPY apps/frontend/index.html ./
COPY apps/frontend/Trunk.toml ./
COPY apps/frontend/assets ./assets/
COPY apps/frontend/.cargo ./.cargo/
COPY crates/shared/src ../../crates/shared/src/

# Verify wasm-bindgen versions match
RUN echo "Verifying wasm-bindgen versions..." && \
    wasm-bindgen --version && \
    echo "Cargo.toml wasm-bindgen version:" && \
    grep 'wasm-bindgen.*=' Cargo.toml || true

# Build the actual application
RUN cargo build --target=wasm32-unknown-unknown --release

# Build with trunk
RUN trunk build --release

FROM nginx:alpine
COPY --from=builder /app/apps/frontend/dist /usr/share/nginx/html
COPY apps/frontend/nginx.conf /etc/nginx/conf.d/default.conf
COPY apps/frontend/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
EXPOSE 80
ENTRYPOINT ["/entrypoint.sh"]
