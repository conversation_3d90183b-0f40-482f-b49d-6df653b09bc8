#!/bin/bash

# Test script to verify automatic pod restarts work
# This script demonstrates that pods restart automatically when <PERSON><PERSON> deploys

set -e

echo "🧪 Testing automatic pod restart functionality..."
echo ""

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not available. Please ensure you're connected to a Kubernetes cluster."
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    echo "❌ helm is not available. Please install Helm."
    exit 1
fi

# Function to get pod creation times
get_pod_times() {
    echo "📊 Current pod creation times:"
    kubectl get pods -l app.kubernetes.io/instance=warda -o custom-columns="NAME:.metadata.name,CREATED:.metadata.creationTimestamp" --no-headers 2>/dev/null || echo "No pods found"
    echo ""
}

# Function to get deployment restart annotations
get_restart_annotations() {
    echo "🏷️  Current restart annotations:"
    for deployment in frontend warda-backend warda-keycloak warda-postgresql; do
        annotation=$(kubectl get deployment $deployment -o jsonpath='{.spec.template.metadata.annotations.deployment\.kubernetes\.io/restart}' 2>/dev/null || echo "not found")
        echo "  $deployment: $annotation"
    done
    echo ""
}

echo "=== BEFORE DEPLOYMENT ==="
get_pod_times
get_restart_annotations

echo "🚀 Running Helm deployment..."
echo "This should trigger automatic pod restarts due to the timestamp annotations."
echo ""

# Run helm deployment
if helm upgrade --install warda helm/warda -f helm/warda/values.yaml --timeout=300s --wait --wait-for-jobs; then
    echo "✅ Helm deployment completed successfully"
else
    echo "❌ Helm deployment failed"
    exit 1
fi

echo ""
echo "=== AFTER DEPLOYMENT ==="
get_pod_times
get_restart_annotations

echo "🎉 Test completed!"
echo ""
echo "💡 What happened:"
echo "   - Each deployment template now includes a 'deployment.kubernetes.io/restart' annotation"
echo "   - This annotation contains the current timestamp ({{ now | quote }})"
echo "   - When Helm deploys, the timestamp changes, forcing Kubernetes to restart the pods"
echo "   - This ensures pods always use the latest images, even with 'latest' tags"
echo ""
echo "🔧 Manual restart commands are also available:"
echo "   make restart-frontend  - Restart just the frontend"
echo "   make restart-backend   - Restart just the backend"
echo "   make restart-all       - Restart all deployments"
