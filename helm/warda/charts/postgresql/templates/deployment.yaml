apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "postgresql.fullname" . }}
  labels:
    {{ include "postgresql.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      {{ include "postgresql.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        # Force pod restart on each deployment
        deployment.kubernetes.io/restart: {{ now | quote }}
      labels:
        {{ include "postgresql.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: postgres
          image: postgres:15
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: {{ include "postgresql.fullname" . }}
                  key: postgres-db
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "postgresql.fullname" . }}
                  key: postgres-username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "postgresql.fullname" . }}
                  key: postgres-password
          volumeMounts:
            - name: postgres-data
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: postgres-data
          emptyDir: {}